// Global state
let cart = [];
let wishlist = [];
let currentHeroSlide = 0;




// Mobile menu toggle
const mobileMenuBtn = document.getElementById('mobile-menu-btn');
const mobileMenu = document.getElementById('mobile-menu');

mobileMenuBtn.addEventListener('click', () => {
    mobileMenu.classList.toggle('hidden');
});

// Mobile submenu toggle
window.toggleMobileSubmenu = function(category) {
    const submenu = document.getElementById(category + '-submenu');
    const arrow = document.getElementById(category + '-arrow');

    submenu.classList.toggle('hidden');
    arrow.classList.toggle('rotate-180');
};

// Search functionality
const searchInput = document.getElementById('search-input');
const searchSuggestions = document.getElementById('search-suggestions');

searchInput?.addEventListener('focus', () => {
    searchSuggestions.classList.remove('hidden');
});

searchInput?.addEventListener('blur', () => {
    setTimeout(() => {
        searchSuggestions.classList.add('hidden');
    }, 200);
});

// Cart functionality
const cartBtn = document.getElementById('cart-btn');
const cartDropdown = document.getElementById('cart-dropdown');
const cartCount = document.getElementById('cart-count');
const cartItems = document.getElementById('cart-items');
const cartTotal = document.getElementById('cart-total');

// Only add dropdown functionality if cart dropdown exists (on index page)
if (cartDropdown) {
    cartBtn?.addEventListener('click', (e) => {
        e.preventDefault();
        cartDropdown.classList.toggle('opacity-0');
        cartDropdown.classList.toggle('invisible');
    });
}

// Wishlist functionality
const wishlistCount = document.getElementById('wishlist-count');

function updateCartUI() {
    const totalItems = cart.reduce((sum, item) => sum + (item.quantity || 1), 0);
    cartCount.textContent = totalItems;
    const total = cart.reduce((sum, item) => sum + (item.price * (item.quantity || 1)), 0);
    if (cartTotal) {
        cartTotal.textContent = `$${total.toFixed(2)}`;
    }

    if (cartItems) {
        if (cart.length === 0) {
            cartItems.innerHTML = '<div class="text-center text-gray-500 py-8">Your cart is empty</div>';
        } else {
            cartItems.innerHTML = cart.map(item => `
                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div class="w-12 h-12 bg-secondary rounded-lg"></div>
                    <div class="flex-1">
                        <h4 class="font-medium text-sm">${item.name}</h4>
                        <p class="text-primary font-bold text-sm">$${item.price} ${item.quantity > 1 ? `x${item.quantity}` : ''}</p>
                    </div>
                    <button onclick="removeFromCart('${item.id}')" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }
    }
}

function updateWishlistUI() {
    wishlistCount.textContent = wishlist.length;
}

window.addToCart = function(productId, productName, productPrice) {
    const existingItemIndex = cart.findIndex(item => item.id === productId);

    if (existingItemIndex > -1) {
        // Item already exists, increase quantity
        cart[existingItemIndex].quantity = (cart[existingItemIndex].quantity || 1) + 1;
    } else {
        // New item, add to cart
        const item = { id: productId, name: productName, price: parseFloat(productPrice), quantity: 1 };
        cart.push(item);
    }

    updateCartUI();

    // Show success message
    showNotification('Added to cart!', 'success');
};

window.removeFromCart = function(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartUI();
};

window.toggleWishlist = function(productId, productName, productPrice) {
    const existingIndex = wishlist.findIndex(item => item.id === productId);

    if (existingIndex > -1) {
        wishlist.splice(existingIndex, 1);
        showNotification('Removed from wishlist', 'info');
    } else {
        wishlist.push({ id: productId, name: productName, price: parseFloat(productPrice) || 99.99 });
        showNotification('Added to wishlist!', 'success');
    }

    updateWishlistUI();
};

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-24 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Hero slider
const heroSlides = document.querySelectorAll('.hero-slide');
const heroDots = document.querySelectorAll('.hero-dot');
const heroPrev = document.getElementById('hero-prev');
const heroNext = document.getElementById('hero-next');

function updateHeroSlider() {
    heroSlides.forEach((slide, index) => {
        slide.style.opacity = index === currentHeroSlide ? '1' : '0';
    });

    heroDots.forEach((dot, index) => {
        if (index === currentHeroSlide) {
            dot.classList.add('bg-primary');
            dot.classList.remove('bg-gray-300');
        } else {
            dot.classList.remove('bg-primary');
            dot.classList.add('bg-gray-300');
        }
    });
}

heroPrev?.addEventListener('click', () => {
    currentHeroSlide = currentHeroSlide === 0 ? heroSlides.length - 1 : currentHeroSlide - 1;
    updateHeroSlider();
});

heroNext?.addEventListener('click', () => {
    currentHeroSlide = currentHeroSlide === heroSlides.length - 1 ? 0 : currentHeroSlide + 1;
    updateHeroSlider();
});

heroDots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
        currentHeroSlide = index;
        updateHeroSlider();
    });
});

// Auto-advance hero slider
setInterval(() => {
    currentHeroSlide = currentHeroSlide === heroSlides.length - 1 ? 0 : currentHeroSlide + 1;
    updateHeroSlider();
}, 7000);

// Product filtering
const filterBtns = document.querySelectorAll('.product-filter-btn');
const productCards = document.querySelectorAll('.product-card');

filterBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        const filter = btn.dataset.filter;

        // Update active button with enhanced styling
        filterBtns.forEach(b => {
            b.classList.remove('active');
            // Reset to default glassmorphism style
            b.style.background = 'rgba(255, 255, 255, 0.9)';
            b.style.color = '#6B7280';
            b.style.transform = 'translateY(0)';
            b.style.boxShadow = '';
        });

        // Apply active styling
        btn.classList.add('active');

        // Filter products with animation
        let visibleCount = 0;
        productCards.forEach((card, index) => {
            const category = card.dataset.category;
            if (filter === 'all' || category === filter) {
                card.style.display = 'block';
                // Stagger animation for visible cards
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 50);
                visibleCount++;
            } else {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.display = 'none';
                }, 300);
            }
        });

        document.getElementById('product-count').textContent = visibleCount;
    });
});

// Product interactions
document.querySelectorAll('.wishlist-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const productCard = btn.closest('.product-card');
        const productId = Math.random().toString(36).substr(2, 9);
        const productName = productCard.querySelector('h4').textContent;
        const productPrice = productCard.dataset.price;

        toggleWishlist(productId, productName, productPrice);

        const icon = btn.querySelector('i');
        icon.classList.toggle('fas');
        icon.classList.toggle('far');
        icon.classList.toggle('text-red-500');
    });
});

document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const productCard = btn.closest('.product-card');
        const productId = Math.random().toString(36).substr(2, 9);
        const productName = productCard.querySelector('h4').textContent;
        const productPrice = productCard.dataset.price;

        addToCart(productId, productName, productPrice);
    });
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});

// Navbar scroll effect
window.addEventListener('scroll', () => {
    const navbar = document.getElementById('navbar');
    if (window.scrollY > 100) {
        navbar.classList.add('bg-white/98', 'shadow-2xl');
        navbar.classList.remove('bg-white/90');
    } else {
        navbar.classList.remove('bg-white/98', 'shadow-2xl');
        navbar.classList.add('bg-white/90');
    }
});

// Authentication Modal Functions
window.openLoginModal = function() {
    const modal = document.getElementById('login-modal');
    const content = document.getElementById('login-modal-content');
    modal.classList.remove('hidden');
    setTimeout(() => {
        content.classList.remove('scale-95');
        content.classList.add('scale-100');
    }, 10);
    document.body.style.overflow = 'hidden';
};

window.openSignupModal = function() {
    const modal = document.getElementById('signup-modal');
    const content = document.getElementById('signup-modal-content');
    modal.classList.remove('hidden');
    setTimeout(() => {
        content.classList.remove('scale-95');
        content.classList.add('scale-100');
    }, 10);
    document.body.style.overflow = 'hidden';
};

window.closeAuthModal = function() {
    const loginModal = document.getElementById('login-modal');
    const signupModal = document.getElementById('signup-modal');
    const loginContent = document.getElementById('login-modal-content');
    const signupContent = document.getElementById('signup-modal-content');

    loginContent.classList.add('scale-95');
    signupContent.classList.add('scale-95');

    setTimeout(() => {
        loginModal.classList.add('hidden');
        signupModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }, 300);
};

window.switchToSignup = function() {
    const loginModal = document.getElementById('login-modal');
    const signupModal = document.getElementById('signup-modal');
    const loginContent = document.getElementById('login-modal-content');
    const signupContent = document.getElementById('signup-modal-content');

    loginContent.classList.add('scale-95');
    setTimeout(() => {
        loginModal.classList.add('hidden');
        signupModal.classList.remove('hidden');
        setTimeout(() => {
            signupContent.classList.remove('scale-95');
            signupContent.classList.add('scale-100');
        }, 10);
    }, 150);
};

window.switchToLogin = function() {
    const loginModal = document.getElementById('login-modal');
    const signupModal = document.getElementById('signup-modal');
    const loginContent = document.getElementById('login-modal-content');
    const signupContent = document.getElementById('signup-modal-content');

    signupContent.classList.add('scale-95');
    setTimeout(() => {
        signupModal.classList.add('hidden');
        loginModal.classList.remove('hidden');
        setTimeout(() => {
            loginContent.classList.remove('scale-95');
            loginContent.classList.add('scale-100');
        }, 10);
    }, 150);
};

// Password visibility toggle
window.togglePasswordVisibility = function(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
};

// Form validation
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function checkPasswordStrength(password) {
    let strength = 0;
    const checks = [
        password.length >= 8,
        /[a-z]/.test(password),
        /[A-Z]/.test(password),
        /[0-9]/.test(password),
        /[^A-Za-z0-9]/.test(password)
    ];

    strength = checks.filter(Boolean).length;
    return strength;
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    updateCartUI();
    updateWishlistUI();
    updateHeroSlider();

    // Real-time validation for signup form
    document.getElementById('signup-email')?.addEventListener('input', function() {
        const email = this.value;
        const validation = document.getElementById('email-validation');

        if (email && !validateEmail(email)) {
            validation.classList.remove('hidden');
            this.classList.add('border-red-500');
        } else {
            validation.classList.add('hidden');
            this.classList.remove('border-red-500');
        }
    });

    document.getElementById('signup-password')?.addEventListener('input', function() {
        const password = this.value;
        const strengthIndicator = document.getElementById('password-strength');
        const strengthText = document.getElementById('strength-text');

        if (password) {
            strengthIndicator.classList.remove('hidden');
            const strength = checkPasswordStrength(password);

            // Reset all strength indicators
            for (let i = 1; i <= 4; i++) {
                const indicator = document.getElementById(`strength-${i}`);
                indicator.classList.remove('bg-red-500', 'bg-yellow-500', 'bg-green-500');
                indicator.classList.add('bg-gray-200');
            }

            // Update strength indicators
            const colors = ['bg-red-500', 'bg-red-500', 'bg-yellow-500', 'bg-green-500'];
            const texts = ['Very Weak', 'Weak', 'Fair', 'Strong'];

            for (let i = 1; i <= Math.min(strength, 4); i++) {
                const indicator = document.getElementById(`strength-${i}`);
                indicator.classList.remove('bg-gray-200');
                indicator.classList.add(colors[strength - 1]);
            }

            strengthText.textContent = texts[Math.min(strength - 1, 3)] || 'Very Weak';
            strengthText.className = `text-sm ${strength >= 3 ? 'text-green-600' : strength >= 2 ? 'text-yellow-600' : 'text-red-600'}`;
        } else {
            strengthIndicator.classList.add('hidden');
        }
    });

    document.getElementById('signup-confirm-password')?.addEventListener('input', function() {
        const password = document.getElementById('signup-password').value;
        const confirmPassword = this.value;
        const matchIndicator = document.getElementById('password-match');

        if (confirmPassword && password !== confirmPassword) {
            matchIndicator.classList.remove('hidden');
            this.classList.add('border-red-500');
        } else {
            matchIndicator.classList.add('hidden');
            this.classList.remove('border-red-500');
        }
    });

    // Form submissions
    document.getElementById('login-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;

        // Simulate login process
        showNotification('Logging in...', 'info');

        setTimeout(() => {
            showNotification('Login successful!', 'success');
            closeAuthModal();
        }, 1500);
    });

    document.getElementById('signup-form')?.addEventListener('submit', function(e) {
        e.preventDefault();
        const name = document.getElementById('signup-name').value;
        const email = document.getElementById('signup-email').value;
        const password = document.getElementById('signup-password').value;
        const confirmPassword = document.getElementById('signup-confirm-password').value;
        const termsAccepted = document.getElementById('terms-checkbox').checked;

        // Validation
        if (!validateEmail(email)) {
            showNotification('Please enter a valid email address', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showNotification('Passwords do not match', 'error');
            return;
        }

        if (checkPasswordStrength(password) < 2) {
            showNotification('Please choose a stronger password', 'error');
            return;
        }

        if (!termsAccepted) {
            showNotification('Please accept the terms of service', 'error');
            return;
        }

        // Simulate signup process
        showNotification('Creating account...', 'info');

        setTimeout(() => {
            showNotification('Account created successfully!', 'success');
            closeAuthModal();
        }, 1500);
    });

    // Close modals when clicking outside
    document.getElementById('login-modal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeAuthModal();
        }
    });

    document.getElementById('signup-modal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeAuthModal();
        }
    });

    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAuthModal();
        }
    });

    // Newsletter subscription
    const newsletterSection = document.querySelector('section input[type="email"]')?.closest('section');
    const emailInput = newsletterSection?.querySelector('input[type="email"]');
    const subscribeBtn = newsletterSection?.querySelector('button');

    subscribeBtn?.addEventListener('click', (e) => {
        e.preventDefault();
        const email = emailInput.value.trim();

        if (email && email.includes('@')) {
            subscribeBtn.textContent = 'Subscribed!';
            subscribeBtn.classList.add('bg-green-500');
            emailInput.value = '';

            setTimeout(() => {
                subscribeBtn.textContent = 'Subscribe';
                subscribeBtn.classList.remove('bg-green-500');
            }, 3000);
        } else {
            emailInput.classList.add('border-red-500');
            setTimeout(() => {
                emailInput.classList.remove('border-red-500');
            }, 3000);
        }
    });

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
            }
        });
    }, observerOptions);

    document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
    });

    // Category Slider Functionality
    let currentCategorySlide = 0;
    const categorySlider = document.getElementById('category-slider');
    const categoryPrevBtn = document.getElementById('category-prev');
    const categoryNextBtn = document.getElementById('category-next');
    const categoryIndicators = document.querySelectorAll('.category-indicator');

    // Calculate how many categories to show based on screen size
    function getCategoriesPerView() {
        const width = window.innerWidth;
        if (width >= 1280) return 6; // xl screens
        if (width >= 1024) return 5; // lg screens
        if (width >= 768) return 4;  // md screens
        if (width >= 640) return 3;  // sm screens
        return 2; // mobile
    }

    function updateCategorySlider() {
        const categoriesPerView = getCategoriesPerView();
        const categoryWidth = 160; // 128px width + 32px margin
        const totalCategories = 15;
        const maxSlides = Math.max(0, totalCategories - categoriesPerView);

        // Ensure currentCategorySlide is within bounds
        currentCategorySlide = Math.min(currentCategorySlide, maxSlides);

        const translateX = -currentCategorySlide * categoryWidth;
        categorySlider.style.transform = `translateX(${translateX}px)`;

        // Update indicators
        categoryIndicators.forEach((indicator, index) => {
            const slideRange = Math.ceil(maxSlides / (categoryIndicators.length - 1));
            const isActive = Math.floor(currentCategorySlide / slideRange) === index;

            if (isActive) {
                indicator.classList.add('bg-primary');
                indicator.classList.remove('bg-gray-300');
            } else {
                indicator.classList.remove('bg-primary');
                indicator.classList.add('bg-gray-300');
            }
        });

        // Update button states
        categoryPrevBtn.style.opacity = currentCategorySlide === 0 ? '0.5' : '1';
        categoryNextBtn.style.opacity = currentCategorySlide >= maxSlides ? '0.5' : '1';
    }

    categoryPrevBtn?.addEventListener('click', () => {
        if (currentCategorySlide > 0) {
            currentCategorySlide--;
            updateCategorySlider();
        }
    });

    categoryNextBtn?.addEventListener('click', () => {
        const categoriesPerView = getCategoriesPerView();
        const maxSlides = Math.max(0, 15 - categoriesPerView);
        if (currentCategorySlide < maxSlides) {
            currentCategorySlide++;
            updateCategorySlider();
        }
    });

    // Auto-scroll category slider
    let categoryAutoScroll = setInterval(() => {
        const categoriesPerView = getCategoriesPerView();
        const maxSlides = Math.max(0, 15 - categoriesPerView);

        if (currentCategorySlide >= maxSlides) {
            currentCategorySlide = 0;
        } else {
            currentCategorySlide++;
        }
        updateCategorySlider();
    }, 4000);

    // Pause auto-scroll on hover
    const categorySection = document.querySelector('#category-slider')?.closest('.relative');
    categorySection?.addEventListener('mouseenter', () => {
        clearInterval(categoryAutoScroll);
    });

    categorySection?.addEventListener('mouseleave', () => {
        categoryAutoScroll = setInterval(() => {
            const categoriesPerView = getCategoriesPerView();
            const maxSlides = Math.max(0, 15 - categoriesPerView);

            if (currentCategorySlide >= maxSlides) {
                currentCategorySlide = 0;
            } else {
                currentCategorySlide++;
            }
            updateCategorySlider();
        }, 4000);
    });

    // Update slider on window resize
    window.addEventListener('resize', () => {
        updateCategorySlider();
    });

    // Initialize category slider
    updateCategorySlider();



    // Simulate loading
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 500);
});



// Make category cards clickable to navigate to product pages
function makeCategoryCardsClickable() {
    const categoryCards = document.querySelectorAll('#category-slider .group');
    const categoryNames = ['dresses', 'tops', 'bottoms', 'shoes', 'bags', 'shirts', 'pants', 'jackets', 'shoes', 'accessories', 'jewelry', 'watches', 'sunglasses', 'activewear', 'formal'];

    categoryCards.forEach((card, index) => {
        if (index < categoryNames.length) {
            card.addEventListener('click', () => {
                window.location.href = `products.html?category=${categoryNames[index]}`;
            });
        }
    });
}

// Initialize clickable categories when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        makeCategoryCardsClickable();
    }, 1000); // Wait for category slider to be initialized
});
